Stack trace:
Frame         Function      Args
0007FFFFB760  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB760, 0007FFFFA660) msys-2.0.dll+0x1FE8E
0007FFFFB760  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA38) msys-2.0.dll+0x67F9
0007FFFFB760  000210046832 (000210286019, 0007FFFFB618, 0007FFFFB760, 000000000000) msys-2.0.dll+0x6832
0007FFFFB760  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB760  000210068E24 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA40  00021006A225 (0007FFFFB770, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF834670000 ntdll.dll
7FF8325A0000 KERNEL32.DLL
7FF832170000 KERNELBASE.dll
7FF833150000 USER32.dll
7FF831F40000 win32u.dll
7FF8344B0000 GDI32.dll
7FF831BF0000 gdi32full.dll
7FF831D10000 msvcp_win.dll
7FF831FE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8343F0000 advapi32.dll
7FF8344E0000 msvcrt.dll
7FF834280000 sechost.dll
7FF831F10000 bcrypt.dll
7FF832660000 RPCRT4.dll
7FF831370000 CRYPTBASE.DLL
7FF8320F0000 bcryptPrimitives.dll
7FF833E60000 IMM32.DLL
